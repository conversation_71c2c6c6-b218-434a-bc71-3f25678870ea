package oryx.module.base

import oryx.event.base.Listenable
import oryx.utility.MC
import oryx.value.Configurable

abstract class Module : MC, Listenable, Configurable() {
    val name: String = javaClass.simpleName
    val category = determineCategory()
    var key = 0
    var enabled = false
        set(value) {
            if (field != value) {
                field = value
                if (value) {
                    enable()
                    register()
                } else {
                    unregister()
                    disable()
                }
            }
        }

    private fun determineCategory(): Category {
        val packageName = javaClass.`package`.name
        val category = packageName.substringAfterLast(".").uppercase()
        return Category.valueOf(category)
    }

    fun toggle() {
        enabled = !enabled
    }

    protected open fun enable() {}
    protected open fun disable() {}
}