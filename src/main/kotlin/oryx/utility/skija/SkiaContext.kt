package oryx.utility.skija

import io.github.humbleui.skija.*
import net.minecraft.client.renderer.GlStateManager
import org.lwjgl.opengl.GL11.*
import org.lwjgl.opengl.GL13.*
import org.lwjgl.opengl.GL14.*
import org.lwjgl.opengl.GL14.glBlendEquation
import org.lwjgl.opengl.GL33.glBindSampler
import oryx.utility.MC
import java.util.function.Consumer

object SkiaContext : MC {
    private var context: DirectContext? = null
    private var surface: Surface? = null
    private var renderTarget: BackendRenderTarget? = null
    val canvas
        get() = surface!!.canvas

    fun createSurface(width: Int, height: Int) {
        if (context == null) {
            context = DirectContext.makeGL()
        }
        if (surface != null) {
            surface!!.close()
            surface = null
        }
        if (renderTarget != null) {
            renderTarget!!.close()
            renderTarget = null
        }
        renderTarget = BackendRenderTarget.makeGL(
            width, height, 0, 8,
            mc.framebuffer.framebufferTexture, GL_RGBA8
        )
        surface = Surface.wrapBackendRenderTarget(
            context!!, renderTarget!!, SurfaceOrigin.BOTTOM_LEFT,
            SurfaceColorFormat.RGBA_8888, ColorSpace.getSRGB()
        )
    }

    fun draw(drawingLogic: Consumer<Canvas>) {
        glPixelStorei(GL_UNPACK_ROW_LENGTH, 0)
        glPixelStorei(GL_UNPACK_SKIP_PIXELS, 0)
        glPixelStorei(GL_UNPACK_SKIP_ROWS, 0)
        glPixelStorei(GL_UNPACK_ALIGNMENT, 4)
        GlStateManager.clearColor(0f, 0f, 0f, 0f)

        context?.resetGLAll()
        drawingLogic.accept(canvas)
        context?.flush()

        glBindSampler(0, 0)
        GlStateManager.disableBlend()
        glDisable(GL_BLEND)
        GlStateManager.blendFunc(GL_SRC_ALPHA, GL_ONE)
        glBlendFunc(GL_SRC_ALPHA, GL_ONE)
        glBlendEquation(GL_FUNC_ADD)
        GlStateManager.colorMask(true, true, true, true)
        glColorMask(true, true, true, true)
        GlStateManager.depthMask(true)
        glDepthMask(true)
        //glDisable(GL_SCISSOR_TEST)
        //glDisable(GL_STENCIL_TEST)
        //glDisable(GL_DEPTH_TEST)
        glActiveTexture(GL_TEXTURE0)
        GlStateManager.setActiveTexture(GL_TEXTURE0)
        //glDisable(GL_CULL_FACE)
        GlStateManager.disableCull()
    }
}