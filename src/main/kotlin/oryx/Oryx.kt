package oryx

import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
import org.lwjgl.opengl.Display
import oryx.command.base.CommandManager
import oryx.module.base.ModuleManager
import oryx.utility.mc
import oryx.utility.rotation.RotationManager
import oryx.utility.skija.SkiaContext

object Oryx {
    const val NAME = "Oryx"
    const val VERSION = "1.0"
    const val PREFIX = "§8[§l§c$NAME§r§8]§r » "

    val LOGGER: Logger = LogManager.getLogger()

    fun init() {
        Display.setTitle("$NAME $VERSION")

        ModuleManager
        CommandManager

        RotationManager
    }

    fun shutdown() {

    }
}