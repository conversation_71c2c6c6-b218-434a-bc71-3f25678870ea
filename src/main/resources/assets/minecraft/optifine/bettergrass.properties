# Configuration for OptiFine's Better Grass feature
# Location: /assets/minecraft/optifine/bettergrass.properties
# Blocks
# Enable Better Grass for specific blocks
grass=true
mycelium=true
podzol=true
# Snowy blocks
# Enable Better Grass for specific blocks which have snow on top
grass.snow=true
mycelium.snow=true
podzol.snow=true
# Multilayer grass sides
# - layer 1 = grass_side
# - layer 2 = grass (colored by biome)
# Allows transparent grass texture to be used as overlay for the grass side
grass.multilayer=false
# Textures
# Configure which textures to be used 
# The "texture.grass" is colored by biome
texture.grass=blocks/grass_top
texture.grass_side=blocks/grass_side
texture.mycelium=blocks/mycelium_top
texture.podzol=blocks/dirt_podzol_top
texture.snow=blocks/snow




