package oryx.utility.skija

import io.github.humbleui.skija.ClipMode
import io.github.humbleui.skija.Paint
import io.github.humbleui.skija.Path
import io.github.humbleui.types.RRect
import io.github.humbleui.types.Rect
import java.awt.Color

object Skia {

    fun save() = SkiaContext.canvas.save()
    fun restore() = SkiaContext.canvas.restore()
    fun scale(scale: Float) = SkiaContext.canvas.scale(scale, scale)
    fun translate(x: Float, y: Float) = SkiaContext.canvas.translate(x, y)
    fun alpha(alpha: Int) = SkiaContext.canvas.saveLayer(null, Paint().setAlpha(alpha))

    fun clip(x: Float, y: Float, width: Float, height: Float, radius: Float) {
        val path = Path()
        path.addRRect(RRect.makeXYWH(x, y, width, height, radius))
        clipPath(path)
    }

    fun clipPath(path: Path) = SkiaContext.canvas.clipPath(path, ClipMode.INTERSECT, true)

    fun paint(color: Color) = Paint().setARGB(
        color.alpha,
        color.red,
        color.green,
        color.blue
    )

    fun rect(x: Float, y: Float, width: Float, height: Float, color: Color) = SkiaContext.canvas.drawRect(
        Rect.makeXYWH(x, y, width, height),
        paint(color)
    )

    fun roundedRect(x: Float, y: Float, width: Float, height: Float, radius: Float, color: Color) = SkiaContext.canvas.drawRRect(
        RRect.makeXYWH(x, y, width, height, radius),
        paint(color)
    )
}