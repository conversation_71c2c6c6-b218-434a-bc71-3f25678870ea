package oryx.utility.render.shader

import org.lwjgl.opengl.GL20.*
import oryx.Oryx
import oryx.utility.file.loadShader

class ShaderProgram(fragmentShader: String, vertexShader: String) {
    private val program: Int = createShader(fragmentShader, vertexShader)

    fun bind() = glUseProgram(program)
    fun unbind() = glUseProgram(0)

    fun uniform(name: String, vararg value: Int) = when (value.size) {
        1 -> glUniform1i(glGetUniformLocation(program, name), value[0])
        2 -> glUniform2i(glGetUniformLocation(program, name), value[0], value[1])
        3 -> glUniform3i(glGetUniformLocation(program, name), value[0], value[1], value[2])
        4 -> glUniform4i(glGetUniformLocation(program, name), value[0], value[1], value[2], value[3])
        else -> Oryx.LOGGER.warn("Uniform integer called with a too-small value array (expected 1 to 4, got ${value.size}). Ignoring.")
    }

    fun uniform(name: String, vararg value: Float) = when (value.size) {
        1 -> glUniform1f(glGetUniformLocation(program, name), value[0])
        2 -> glUniform2f(glGetUniformLocation(program, name), value[0], value[1])
        3 -> glUniform3f(glGetUniformLocation(program, name), value[0], value[1], value[2])
        4 -> glUniform4f(glGetUniformLocation(program, name), value[0], value[1], value[2], value[3])
        else -> Oryx.LOGGER.warn("Uniform float called with a too-small value array (expected 1 to 4, got ${value.size}). Ignoring.")
    }

    private fun createShader(fragmentShader: String, vertexShader: String): Int {
        if (fragmentShader.isEmpty() || vertexShader.isEmpty()) return 0

        val fragment = loadShader(fragmentShader)
        val vertex = loadShader(vertexShader)

        val fragmentId = glCreateShader(GL_FRAGMENT_SHADER)
        val vertexId = glCreateShader(GL_VERTEX_SHADER)

        glShaderSource(fragmentId, fragment)
        glCompileShader(fragmentId)

        if (glGetShaderi(fragmentId, GL_COMPILE_STATUS) == 0) {
            val log = glGetShaderInfoLog(fragmentId, 1024)
            Oryx.LOGGER.error("Fragment shader compilation failed for $fragmentShader: $log")
            glDeleteShader(fragmentId)
            glDeleteShader(vertexId)
            return 0
        }

        glShaderSource(vertexId, vertex)
        glCompileShader(vertexId)

        if (glGetShaderi(vertexId, GL_COMPILE_STATUS) == 0) {
            val log = glGetShaderInfoLog(vertexId, 1024)
            Oryx.LOGGER.error("Vertex shader compilation failed for $vertexShader: $log")
            glDeleteShader(fragmentId)
            glDeleteShader(vertexId)
            return 0
        }

        val programId = glCreateProgram()
        glAttachShader(programId, fragmentId)
        glAttachShader(programId, vertexId)
        glLinkProgram(programId)

        if (glGetProgrami(programId, GL_LINK_STATUS) == 0) {
            val log = glGetProgramInfoLog(programId, 1024)
            Oryx.LOGGER.error("Shader program linking failed: $log")
            glDeleteProgram(programId)
            glDeleteShader(fragmentId)
            glDeleteShader(vertexId)
            return 0
        }

        glValidateProgram(programId)
        glDeleteShader(fragmentId)
        glDeleteShader(vertexId)

        return programId
    }
}