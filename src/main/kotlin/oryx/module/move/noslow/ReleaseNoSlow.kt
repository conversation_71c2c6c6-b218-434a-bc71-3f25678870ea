package oryx.module.move.noslow

import net.minecraft.network.play.client.C07PacketPlayerDigging
import net.minecraft.util.BlockPos
import net.minecraft.util.EnumFacing
import oryx.event.impl.UpdateEvent

class ReleaseNoSlow : NoSlowMode() {
    override fun onUpdate(event: UpdateEvent) {
        mc.netHandler.addToSendQueue(
            C07PacketPlayerDigging(
                C07PacketPlayerDigging.Action.RELEASE_USE_ITEM, BlockPos.ORIGIN,
                EnumFacing.DOWN
            )
        )
    }

}